# 云链IoT平台工程架构图

## 项目概述

云链IoT平台（cloud-link-iot）是基于Spring Boot 3.2 + Spring Cloud微服务架构的企业级物联网管理平台，支持多协议设备接入、实时数据处理、规则引擎、设备管理等完整的IoT解决方案。

## 系统架构总览

```mermaid
graph TB
    subgraph "客户端层"
        WebUI[管理控制台<br/>Web界面]
        MobileApp[移动应用<br/>App/小程序]
        APIClient[第三方系统<br/>API调用]
    end

    subgraph "云端IoT平台"
        subgraph "cloud-iot-gateway 网关接入层"
            Gateway[IoT网关服务<br/>IotGatewayServerApplication]
            HttpProtocol[HTTP协议处理器<br/>IotHttpUpstreamProtocol]
            MqttProtocol[MQTT协议处理器<br/>IotEmqxUpstreamProtocol] 
            TcpProtocol[TCP协议处理器<br/>IotTcpUpstreamProtocol]
            AuthService[设备认证服务<br/>IotEmqxAuthEventProtocol]
        end
        
        subgraph "cloud-iot-api RPC接口层"
            DeviceAPI["设备管理API<br/>@FeignClient"]
            ProductAPI["产品管理API<br/>@FeignClient"]
            AuthAPI["认证授权API<br/>@FeignClient"]
        end
        
        subgraph "cloud-iot-biz 核心业务层"
            MainApp[主应用服务<br/>IotServerApplication]
            
            subgraph "业务服务"
                DeviceService[设备管理服务<br/>DeviceService]
                ProductService[产品管理服务<br/>ProductService]
                IndexService[统计分析服务<br/>IndexService]
                TransportService[数据转发服务<br/>TransportRuleService]
            end
            
            subgraph "消息处理"
                CommandReceiver["命令接收器<br/>@RocketMQMessageListener"]
                MqttPublisher["MQTT发布器<br/>@Service"]
                MessageProcessor[消息处理器]
            end
            
            subgraph "规则引擎"
                RuleEngine[场景规则引擎<br/>SceneRuleService]
                RuleAction[规则动作执行<br/>SceneRuleActionService]
                RuleTrigger[规则触发器<br/>SceneRuleTriggerService]
            end
        end
        
        subgraph "消息中间件"
            RocketMQ[RocketMQ消息队列<br/>异步消息处理]
            Redis[Redis缓存<br/>会话/配置缓存]
        end
        
        subgraph "数据存储层"
            MySQL[(MySQL数据库<br/>业务数据存储)]
            TDengine[(TDengine时序数据库<br/>设备数据存储)]
        end
    end
    
    subgraph "设备层"
        IoTDevice[IoT智能设备<br/>传感器/执行器]
        EdgeGateway[边缘网关<br/>协议转换]
        DirectDevice[直连设备<br/>4G/WiFi模组]
    end
    
    subgraph "外部依赖"
        NacosRegistry[Nacos注册中心<br/>服务发现]
        NacosConfig[Nacos配置中心<br/>配置管理]
        SystemService[系统服务<br/>用户权限管理]
        ThirdParty[第三方系统<br/>数据推送]
    end

    %% 设备接入数据流
    IoTDevice -->|"MQTT/HTTP/TCP<br/>数据上报"| Gateway
    EdgeGateway -->|"批量数据<br/>协议转换"| Gateway
    DirectDevice -->|"直连通信<br/>实时数据"| Gateway
    
    Gateway -->|"协议适配"| HttpProtocol
    Gateway -->|"协议适配"| MqttProtocol
    Gateway -->|"协议适配"| TcpProtocol
    
    HttpProtocol -->|"设备认证"| AuthService
    MqttProtocol -->|"设备认证"| AuthService
    TcpProtocol -->|"设备认证"| AuthService
    
    AuthService -->|"消息路由"| RocketMQ
    
    %% 业务处理流程
    RocketMQ -->|"消息消费"| CommandReceiver
    CommandReceiver -->|"数据解析"| MessageProcessor
    MessageProcessor -->|"业务处理"| DeviceService
    MessageProcessor -->|"规则匹配"| RuleEngine
    
    RuleEngine -->|"触发条件"| RuleTrigger
    RuleEngine -->|"执行动作"| RuleAction
    RuleAction -->|"设备控制"| MqttPublisher
    RuleAction -->|"数据转发"| TransportService
    
    %% 管理后台流程
    WebUI -->|"管理操作"| DeviceAPI
    WebUI -->|"管理操作"| ProductAPI
    WebUI -->|"管理操作"| AuthAPI
    MobileApp -->|"移动管理"| DeviceAPI
    APIClient -->|"第三方调用"| DeviceAPI
    
    DeviceAPI -->|"RPC调用"| DeviceService
    ProductAPI -->|"RPC调用"| ProductService
    AuthAPI -->|"RPC调用"| AuthService
    
    %% 数据存储
    DeviceService -.->|"设备信息"| MySQL
    ProductService -.->|"产品配置"| MySQL
    MessageProcessor -.->|"时序数据"| TDengine
    IndexService -.->|"统计数据"| TDengine
    
    %% 缓存使用
    DeviceService -.->|"设备状态"| Redis
    AuthService -.->|"认证会话"| Redis
    RuleEngine -.->|"规则缓存"| Redis
    
    %% 外部依赖
    Gateway -.->|"服务注册"| NacosRegistry
    MainApp -.->|"配置获取"| NacosConfig
    AuthService -.->|"用户验证"| SystemService
    
    %% 控制指令下发
    MqttPublisher -->|"指令下发"| Gateway
    Gateway -->|"控制指令"| IoTDevice
    TransportService -->|"数据推送"| ThirdParty

    %% 样式定义
    classDef clientClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gatewayClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef apiClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef businessClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef messageClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataClass fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef deviceClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef externalClass fill:#fafafa,stroke:#424242,stroke-width:2px

    class WebUI,MobileApp,APIClient clientClass
    class Gateway,HttpProtocol,MqttProtocol,TcpProtocol,AuthService gatewayClass
    class DeviceAPI,ProductAPI,AuthAPI apiClass
    class MainApp,DeviceService,ProductService,IndexService,TransportService,CommandReceiver,MqttPublisher,MessageProcessor,RuleEngine,RuleAction,RuleTrigger businessClass
    class RocketMQ,Redis messageClass
    class MySQL,TDengine dataClass
    class IoTDevice,EdgeGateway,DirectDevice deviceClass
    class NacosRegistry,NacosConfig,SystemService,ThirdParty externalClass
```

## 详细技术架构

```mermaid
graph TB
    subgraph "技术架构分层"
        subgraph "表现层 Presentation Layer"
            UI[前端界面<br/>React/Vue.js]
            API_Gateway[API网关<br/>Spring Cloud Gateway]
            RestAPI[RESTful API<br/>Spring MVC]
        end
        
        subgraph "服务层 Service Layer"
            BizService["业务服务<br/>@Service组件"]
            RPC_Client[RPC客户端<br/>OpenFeign]
            MessageQueue[消息队列<br/>RocketMQ]
            Cache[缓存服务<br/>Redis]
        end
        
        subgraph "应用层 Application Layer"
            SpringBoot[Spring Boot 3.2<br/>应用容器]
            Security[Spring Security<br/>安全框架]
            Validation[参数校验<br/>Hibernate Validator]
        end
        
        subgraph "基础设施层 Infrastructure Layer"
            Database[数据持久化<br/>MyBatis-Plus]
            ConfigCenter[配置中心<br/>Nacos Config]
            Registry[注册中心<br/>Nacos Discovery]
            Monitoring[监控告警<br/>Spring Actuator]
        end
    end
    
    subgraph "协议支持层"
        MQTT[MQTT协议<br/>Vert.x MQTT]
        HTTP[HTTP协议<br/>Spring WebMVC]
        TCP[TCP协议<br/>Vert.x TCP]
        WebSocket[WebSocket<br/>实时通信]
    end
    
    UI --> API_Gateway
    API_Gateway --> RestAPI
    RestAPI --> BizService
    BizService --> RPC_Client
    BizService --> MessageQueue
    BizService --> Cache
    
    SpringBoot --> Security
    SpringBoot --> Validation
    Security --> Database
    
    Database --> ConfigCenter
    ConfigCenter --> Registry
    Registry --> Monitoring
    
    BizService --> MQTT
    BizService --> HTTP
    BizService --> TCP
    BizService --> WebSocket
```

## 模块详细说明

### 🌐 cloud-iot-gateway (网关接入层)

**主要功能:**
- 多协议设备接入支持（HTTP、MQTT、TCP）
- 设备身份认证和授权
- 协议适配和消息转换
- 连接状态管理
- 消息路由和转发

**核心组件:**
- `IotGatewayServerApplication`: 网关服务启动类
- `IotHttpUpstreamProtocol`: HTTP协议处理器
- `IotEmqxUpstreamProtocol`: MQTT协议处理器  
- `IotTcpUpstreamProtocol`: TCP协议处理器
- `IotEmqxAuthEventProtocol`: 设备认证处理器

**技术栈:**
- Spring Boot 3.2
- Vert.x (异步IO框架)
- Nacos (服务注册与配置)
- RocketMQ (消息队列)
- Redis (缓存)

### 💼 cloud-iot-biz (核心业务层)

**主要功能:**
- 设备生命周期管理
- 产品模型定义和管理
- 规则引擎和场景联动
- 数据处理和转发
- 统计分析和监控

**核心服务:**
- `DeviceService`: 设备管理服务
- `ProductService`: 产品管理服务
- `SceneRuleService`: 规则引擎服务
- `TransportRuleService`: 数据转发服务
- `IndexService`: 统计分析服务

**消息处理:**
- `CommandReceiver`: RocketMQ消息消费者
- `MqttPublisher`: MQTT消息发布器

**技术栈:**
- Spring Boot 3.2 + Spring Cloud
- MyBatis-Plus (数据持久化)
- Spring Security (安全框架)
- RocketMQ (消息队列)
- Redis (缓存)
- TDengine (时序数据库)

### 🔌 cloud-iot-api (接口定义层)

**主要功能:**
- RPC接口定义
- Feign客户端配置
- API文档生成
- 数据模型定义

**核心接口:**
- 设备管理API
- 产品管理API
- 认证授权API
- 数据查询API

**技术栈:**
- Spring Cloud OpenFeign
- SpringDoc OpenAPI 3
- Hibernate Validator

## 核心业务流程

### 1️⃣ 设备接入认证流程

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant Gateway as 网关服务
    participant Auth as 认证服务
    participant MQ as RocketMQ
    participant Biz as 业务服务
    participant DB as 数据库

    Device->>Gateway: 1. 连接请求(MQTT/HTTP/TCP)
    Gateway->>Auth: 2. 设备认证
    Auth->>DB: 3. 验证设备凭证
    DB-->>Auth: 4. 返回认证结果
    Auth-->>Gateway: 5. 认证成功/失败

    alt 认证成功
        Gateway->>Device: 6. 连接确认
        Gateway->>MQ: 7. 发送连接事件
        MQ->>Biz: 8. 消费连接事件
        Biz->>DB: 9. 更新设备状态
    else 认证失败
        Gateway->>Device: 6. 拒绝连接
    end
```

### 2️⃣ 数据上报处理流程

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant Gateway as 网关服务
    participant MQ as RocketMQ
    participant Processor as 消息处理器
    participant Rule as 规则引擎
    participant Storage as 存储服务

    Device->>Gateway: 1. 数据上报
    Gateway->>Gateway: 2. 协议解析
    Gateway->>MQ: 3. 发送到消息队列
    MQ->>Processor: 4. 消费数据消息

    Processor->>Storage: 5. 存储时序数据
    Processor->>Rule: 6. 触发规则匹配

    alt 规则匹配成功
        Rule->>Rule: 7. 执行规则动作
        Rule->>Gateway: 8. 下发控制指令
        Gateway->>Device: 9. 设备控制
    end
```

### 3️⃣ 设备控制流程

```mermaid
sequenceDiagram
    participant UI as 管理界面
    participant API as 接口层
    participant Service as 业务服务
    participant Publisher as MQTT发布器
    participant Gateway as 网关服务
    participant Device as IoT设备

    UI->>API: 1. 设备控制请求
    API->>Service: 2. RPC调用
    Service->>Service: 3. 参数验证
    Service->>Publisher: 4. 发布控制消息
    Publisher->>Gateway: 5. MQTT消息
    Gateway->>Device: 6. 下发控制指令
    Device-->>Gateway: 7. 执行结果反馈
    Gateway-->>Service: 8. 结果通知
    Service-->>UI: 9. 返回执行状态
```

## 数据流向图

### 📈 上行数据流 (设备→云端)
1. **设备连接**: IoT设备通过MQTT/HTTP/TCP协议连接网关
2. **协议解析**: 网关根据协议类型进行消息解析和验证
3. **身份认证**: 验证设备身份，生成会话令牌
4. **消息路由**: 将设备数据发送到RocketMQ消息队列
5. **异步处理**: CommandReceiver消费消息进行业务处理
6. **数据存储**: 时序数据存储到TDengine，配置数据存储到MySQL
7. **规则匹配**: 触发规则引擎进行场景联动和告警处理

### 📉 下行数据流 (云端→设备)
1. **指令发起**: 管理后台或规则引擎发起设备控制指令
2. **API调用**: 通过Feign接口调用业务服务
3. **消息发布**: MqttPublisher发布控制消息到消息队列
4. **网关转发**: 网关接收并根据协议转发指令到目标设备
5. **设备执行**: 设备接收指令并执行相应的控制动作
6. **结果反馈**: 设备将执行结果通过上行数据流反馈给云端

## 部署架构建议

### 🏭 生产环境部署
```
负载均衡器 (Nginx/ALB)
    ↓
API网关集群 (Spring Cloud Gateway)
    ↓
├─ 网关服务集群 (cloud-iot-gateway)
├─ 业务服务集群 (cloud-iot-biz)
└─ 接口服务集群 (cloud-iot-api)
    ↓
├─ 消息队列集群 (RocketMQ)
├─ 缓存集群 (Redis Cluster)
├─ 数据库主从 (MySQL Master-Slave)
└─ 时序数据库 (TDengine Cluster)
```

### 🔧 技术选型说明

| 技术组件 | 选型 | 版本 | 说明 |
|---------|------|------|------|
| 基础框架 | Spring Boot | 3.2.0 | 微服务基础框架 |
| 微服务 | Spring Cloud | 2023.x | 微服务治理 |
| 服务注册 | Nacos | 2.x | 服务发现与配置中心 |
| 消息队列 | RocketMQ | 5.x | 异步消息处理 |
| 数据库 | MySQL | 8.0+ | 业务数据存储 |
| 时序数据库 | TDengine | 3.x | IoT数据存储 |
| 缓存 | Redis | 7.x | 分布式缓存 |
| 异步IO | Vert.x | 4.5.x | 高性能网络框架 |
| ORM框架 | MyBatis-Plus | 3.5.x | 数据持久化 |
| 安全框架 | Spring Security | 6.x | 认证授权 |

## 性能指标

### 📊 系统容量
- **设备接入**: 支持10万+设备并发连接
- **消息吞吐**: 100万条/秒消息处理能力
- **数据存储**: PB级时序数据存储
- **响应时间**: API响应时间<100ms
- **可用性**: 99.9%系统可用性

### 🚀 扩展性设计
- **水平扩展**: 支持服务实例动态扩缩容
- **协议扩展**: 插件化协议适配器
- **规则扩展**: 可视化规则引擎配置
- **存储扩展**: 分库分表支持海量数据

---

## 总结

云链IoT平台采用现代化的微服务架构，具备高可用、高性能、易扩展的特点。通过分层设计和模块化开发，实现了设备接入、数据处理、规则引擎等核心功能的解耦，为企业级IoT应用提供了完整的解决方案。
