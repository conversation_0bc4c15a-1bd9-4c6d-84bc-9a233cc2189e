package cn.powerchina.bjy.link.iot.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 设备时钟同步数据转发
 * @Author: yhx
 * @CreateDate: 2024/9/18
 */
@Data
public class DeviceClockSyncTransportModel implements MessageToEdge, Serializable {

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    private String messageId;

    /**
     * 设备编码
     */
    private String deviceCode;

    private String thingIdentity;

    private String outputParams;

    private Integer resultCode;

    private Integer downConsumeTime;

    private ClockSyncData clockSyncData;

    @Data
    public static class ClockSyncData {

        private Integer code;

        private String msg;

        private String data;

    }
}
