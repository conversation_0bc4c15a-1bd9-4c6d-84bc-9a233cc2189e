package cn.powerchina.bjy.link.iot.controller.admin.appauth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 应用管理认证切换启用状态 Request VO")
@Data
public class AppAuthSwitchReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1323")
    private Long id;

    @Schema(description = "应用状态(1:启用;0:禁用)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

}