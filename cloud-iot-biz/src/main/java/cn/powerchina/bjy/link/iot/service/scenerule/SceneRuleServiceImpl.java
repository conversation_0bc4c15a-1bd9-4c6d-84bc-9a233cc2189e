package cn.powerchina.bjy.link.iot.service.scenerule;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ServiceException;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.RuleSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRuleSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.CommandVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletimetrigger.SceneRuleTimeTriggerDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import cn.powerchina.bjy.link.iot.dal.mysql.scenerule.SceneRuleMapper;
import cn.powerchina.bjy.link.iot.enums.ActionTypeEnum;
import cn.powerchina.bjy.link.iot.enums.ConditionTypeEnum;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import cn.powerchina.bjy.link.iot.enums.TriggerTypeEnum;
import cn.powerchina.bjy.link.iot.framework.rule.RuleRedisCache;
import cn.powerchina.bjy.link.iot.service.alarmtemplate.AlarmTemplateService;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import cn.powerchina.bjy.link.iot.service.sceneruletimetrigger.SceneRuleTimeTriggerService;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 场景规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneRuleServiceImpl implements SceneRuleService {

    @Resource
    private SceneRuleMapper sceneRuleMapper;

    @Resource
    private SceneRuleTimeTriggerService ruleTimeTriggerService;

    @Resource
    private SceneRuleTriggerService ruleTriggerService;

    @Resource
    private SceneRuleActionService sceneRuleActionService;

    @Resource
    @Lazy
    private AlarmTemplateService alarmTemplateService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ChannelTopic ruleUpdateTopic;

    @Resource
    @Lazy
    private RuleRedisCache ruleRedisCache;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private ResourceSpaceService resourceSpaceService;

    @Resource
    private RoleCommon roleCommon;

    @Override
    @Transactional
    public Long createAndUpdateSceneRule(RuleSaveReqVO ruleSaveReqVO) {
        //规则表达式
        StringBuilder expression = new StringBuilder();
        StringBuilder inhibitionExpression = new StringBuilder();
        //触发条件
        StringBuilder triggerExpression = new StringBuilder();
        StringBuilder inhibitionTriggerExpression = new StringBuilder();
        if (CollectionUtil.isNotEmpty(ruleSaveReqVO.getRuleTriggerSaveList())) {
            ruleSaveReqVO.getRuleTriggerSaveList().forEach(item -> {
                if (TriggerTypeEnum.DEVICE_TRIGGER.getType().equals(item.getTriggerType())) {
                    if (triggerExpression.length() > 1) {
                        triggerExpression.append("||");
                        inhibitionTriggerExpression.append("&&");
                    }
                    triggerExpression.append("(").append("productCode == '").append(item.getProductCode()).append("'");
                    inhibitionTriggerExpression.append("(").append("productCode == '").append(item.getProductCode()).append("'");
                    //全部设备：deviceCode=-1
                    if (StringUtils.isNotEmpty(item.getDeviceCode()) && !"-1".equals(item.getDeviceCode())) {
                        triggerExpression.append("&& deviceCode == '").append(item.getDeviceCode()).append("'");
                        inhibitionTriggerExpression.append("&& deviceCode == '").append(item.getDeviceCode()).append("'");
                    }
                    if (StringUtils.isNotEmpty(item.getAttributeExpression())) {
                        triggerExpression.append("&&").append(item.getAttributeExpression());
                        inhibitionTriggerExpression.append("&&").append("!").append("(").append(item.getAttributeExpression()).append(")");
                    }
                    triggerExpression.append(")");
                    inhibitionTriggerExpression.append(")");
                }
            });
        }

        StringBuilder conditionExpression = new StringBuilder();
        StringBuilder inhibitionConditionExpression = new StringBuilder();
        if (CollectionUtil.isNotEmpty(ruleSaveReqVO.getRuleConditionSaveList())) {
            ruleSaveReqVO.getRuleConditionSaveList().forEach(item -> {
                if (conditionExpression.length() > 1) {
                    conditionExpression.append("&&");
                    inhibitionConditionExpression.append("||");
                }
                conditionExpression.append("(");
                inhibitionConditionExpression.append("(");
                if (StringUtils.isNotEmpty(item.getDeviceCode())) {
                    conditionExpression.append("deviceCode_").append(item.getDeviceCode()).append("== '").append(item.getDeviceCode()).append("'");
                    inhibitionConditionExpression.append("deviceCode_").append(item.getDeviceCode()).append("== '").append(item.getDeviceCode()).append("'");
                }
                if (StringUtils.isNotEmpty(item.getAttributeExpression())) {
                    conditionExpression.append("&&").append(item.getDeviceCode()).append("_").append(item.getAttributeExpression());
                    //抑制取非
                    inhibitionConditionExpression.append("&&").append("!").append("(").append(item.getDeviceCode()).append("_").append(item.getAttributeExpression()).append(")");
                }
                conditionExpression.append(")");
                inhibitionConditionExpression.append(")");
            });
            //设置限制条件，必须至少设置一个触发条件。触发条件只有定时触发，不需要括号
            if (!StringUtils.isEmpty(triggerExpression)) {
                expression.append("(").append(triggerExpression).append(")").append(" && ").append("(").append(conditionExpression).append(")");
            } else {
                expression.append(conditionExpression);
            }

            if (!StringUtils.isEmpty(inhibitionTriggerExpression)) {
                inhibitionExpression.append("(").append(inhibitionTriggerExpression).append(")").append(" || ").append("(").append(inhibitionConditionExpression).append(")");

            } else {
                inhibitionExpression.append(inhibitionConditionExpression);
            }
        } else {
            if (CollectionUtil.isNotEmpty(ruleSaveReqVO.getRuleTriggerSaveList())) {
                if (StringUtils.isNotEmpty(triggerExpression)) {
                    expression.append("(").append(triggerExpression).append(")");
                    inhibitionExpression.append("(").append(inhibitionTriggerExpression).append(")");
                }
            }
        }

        ruleSaveReqVO.setRuleExpression(expression.toString());
        ruleSaveReqVO.setInhibitionExpression(inhibitionExpression.toString());

        Long ruleId = ruleSaveReqVO.getId();
        if (Objects.isNull(ruleSaveReqVO.getId())) {
            ruleId = createSceneRule(ruleSaveReqVO);
            //保存action
            ruleSaveReqVO.setId(ruleId);
        }
        ruleSaveReqVO.setRuleAction("executeRuleList.add(" + ruleId + "L)");
        updateSceneRule(ruleSaveReqVO);

        ruleTriggerService.createAndUpdateSceneRuleTrigger(ruleSaveReqVO.getRuleTriggerSaveList(), ruleId, ConditionTypeEnum.TRIGGER_TYPE.getType());
        ruleTriggerService.createAndUpdateSceneRuleTrigger(ruleSaveReqVO.getRuleConditionSaveList(), ruleId, ConditionTypeEnum.CONDITION_TYPE.getType());
        sceneRuleActionService.createAndUpdateSceneRuleAction(ruleSaveReqVO.getRuleActionSaveList(), ruleId);

        // 发布规则更新消息
        redisTemplate.convertAndSend(ruleUpdateTopic.getTopic(), "Rule updated: " + ruleId);
        ruleRedisCache.clearAllSceneRules();
        return ruleId;
    }

    @Override
    public RuleSaveReqVO getSceneRuleTriggerAndAction(Long id) {
        RuleSaveReqVO ruleSaveReqVO = new RuleSaveReqVO();
        SceneRuleDO sceneRule = getSceneRule(id);
        BeanUtil.copyProperties(sceneRule, ruleSaveReqVO);
        List<SceneRuleTriggerDO> ruleTriggerDOList = ruleTriggerService.getSceneRuleTriggerByRuleId(id, ConditionTypeEnum.TRIGGER_TYPE.getType());
        if (CollectionUtil.isNotEmpty(ruleTriggerDOList)) {
            List<SceneRuleTriggerSaveReqVO> ruleTriggerSaveReqVOList = new ArrayList<>();
            ruleTriggerDOList.forEach(trigger -> {
                SceneRuleTriggerSaveReqVO ruleTriggerSaveReqVO = BeanUtils.toBean(trigger, SceneRuleTriggerSaveReqVO.class);
                if (TriggerTypeEnum.TIME_TRIGGER.getType().equals(trigger.getTriggerType())) {
                    SceneRuleTimeTriggerDO ruleTimeTriggerDO = ruleTimeTriggerService.getSceneRuleTimeTriggerByTriggerId(trigger.getId());
                    ruleTriggerSaveReqVO.setRuleTimeTriggerSaveReqVO(BeanUtils.toBean(ruleTimeTriggerDO, SceneRuleTimeTriggerSaveReqVO.class));
                }
                ruleTriggerSaveReqVOList.add(ruleTriggerSaveReqVO);
            });
            ruleSaveReqVO.setRuleTriggerSaveList(ruleTriggerSaveReqVOList);
        }

        List<SceneRuleTriggerDO> ruleConditionDoList = ruleTriggerService.getSceneRuleTriggerByRuleId(id, ConditionTypeEnum.CONDITION_TYPE.getType());
        if (CollectionUtil.isNotEmpty(ruleConditionDoList)) {
            List<SceneRuleTriggerSaveReqVO> ruleConditionList = new ArrayList<>();
            ruleConditionDoList.forEach(condition -> {
                SceneRuleTriggerSaveReqVO ruleTriggerSaveReqVO = BeanUtils.toBean(condition, SceneRuleTriggerSaveReqVO.class);
                ruleConditionList.add(ruleTriggerSaveReqVO);
            });
            ruleSaveReqVO.setRuleConditionSaveList(ruleConditionList);
        }

        List<SceneRuleActionDO> ruleActionDOList = sceneRuleActionService.getSceneRuleActionByRuleId(id);
        if (CollectionUtil.isNotEmpty(ruleActionDOList)) {
            List<SceneRuleActionSaveReqVO> ruleActionSaveList = new ArrayList<>();
            ruleActionDOList.forEach(action -> {
                SceneRuleActionSaveReqVO actionSaveReqVO = BeanUtils.toBean(action, SceneRuleActionSaveReqVO.class);
                if (ActionTypeEnum.ALARM_TYPE.getType().equals(action.getActionType())) {
                    AlarmTemplateSaveReqVO alarmTemplateSaveReqVO = alarmTemplateService.getTemplateAndNotificationByActionId(action.getId());
                    actionSaveReqVO.setAlarmTemplateSave(alarmTemplateSaveReqVO);
                }
                if (ActionTypeEnum.DEVICE_TYPE.getType().equals(action.getActionType())) {
                    String commandConfig = action.getCommandConfig();
                    if (StringUtils.isNotEmpty(commandConfig)) {
                        List<CommandVO> commandVOS = JSONObject.parseArray(commandConfig, CommandVO.class);
                        actionSaveReqVO.setCommandList(commandVOS);
                    }
                }
                ruleActionSaveList.add(actionSaveReqVO);
            });
            ruleSaveReqVO.setRuleActionSaveList(ruleActionSaveList);
        }
        return ruleSaveReqVO;
    }


    @Override
    public void switchSceneRuleStatus(Long id, Integer status) {
        // 校验存在
        validateSceneRuleExists(id);
        // 校验状态值
        if (!Arrays.asList(0, 1).contains(status)) {
            throw new ServiceException(BAD_REQUEST.getCode(), "状态值必须是 0 或 1");
        }
        // 更新状态
        sceneRuleMapper.updateById(SceneRuleDO.builder()
                .id(id)
                .status(status)
                .build());
        redisTemplate.convertAndSend(ruleUpdateTopic.getTopic(), "Rule updated: " + id);
        ruleRedisCache.clearAllSceneRules();
    }


    @Override
    public Long createSceneRule(SceneRuleSaveReqVO createReqVO) {
        // 插入
        SceneRuleDO sceneRule = BeanUtils.toBean(createReqVO, SceneRuleDO.class);
        sceneRuleMapper.insert(sceneRule);
        // 返回
        return sceneRule.getId();
    }

    @Override
    public void updateSceneRule(SceneRuleSaveReqVO updateReqVO) {
        // 校验存在
        SceneRuleDO sceneRuleDO = validateSceneRuleExists(updateReqVO.getId());
        //只有创建者可以修改规则
        checkCreatorPermission(sceneRuleDO);
        // 更新
        SceneRuleDO updateObj = BeanUtils.toBean(updateReqVO, SceneRuleDO.class);
        sceneRuleMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneRule(Long id) {
        // 校验存在
        SceneRuleDO sceneRule = validateSceneRuleExists(id);
        // 校验权限 - 只有创建者可以删除
        checkCreatorPermission(sceneRule);
        //被引用的规则不能被删除
        List<SceneRuleActionDO> ruleActionList = sceneRuleActionService.getRuleActionBySceneId(id);
        if (CollectionUtil.isNotEmpty(ruleActionList)) {
            throw exception(SCENE_RULE_USING);
        }
        // 删除
        sceneRuleMapper.deleteById(id);
        redisTemplate.convertAndSend(ruleUpdateTopic.getTopic(), "Rule updated: " + id);
        ruleRedisCache.clearAllSceneRules();
    }


    /**
     * 校验场景规则是否存在
     *
     * @param id 规则ID
     * @return 场景规则对象
     */
    private SceneRuleDO validateSceneRuleExists(Long id) {
        SceneRuleDO sceneRule = sceneRuleMapper.selectById(id);
        if (sceneRule == null) {
            throw exception(SCENE_RULE_NOT_EXISTS);
        }
        return sceneRule;
    }

    /**
     * 检查是否有权限操作（是否为创建者）
     *
     * @param sceneRule 场景规则对象
     */
    private void checkCreatorPermission(SceneRuleDO sceneRule) {
        // 获取当前登录用户的ID
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 检查当前用户是否为创建者
        if (!String.valueOf(userId).equals(sceneRule.getCreator())) {
            throw exception(SCENE_RULE_NO_PERMISSION);
        }
    }


    @Override
    public SceneRuleDO getSceneRule(Long id) {
        return validateSceneRuleExists(id);
    }

    @Override
    public PageResult<SceneRulePageRespVO> getSceneRulePage(SceneRulePageReqVO pageReqVO) {
        if (ObjectUtil.isNull(pageReqVO.getResourceSpaceId())) {
            List<Long> resourceSpaceIds = roleCommon.getResourceSpaceIds();
            pageReqVO.setResourceSpaceIds(resourceSpaceIds);
        }
        List<SceneRulePageRespVO> pageRuleList = new ArrayList<>();
        PageResult<SceneRuleDO> sceneRuleDOPageResult = sceneRuleMapper.selectPage(pageReqVO);
        if (CollectionUtil.isNotEmpty(sceneRuleDOPageResult.getList())) {

            List<ResourceSpaceDO> spaceList = resourceSpaceService.getResourceSpaceList();
            Map<Long, String> spaceMap = spaceList.stream().collect(Collectors.toMap(
                    ResourceSpaceDO::getId,
                    ResourceSpaceDO::getSpaceName,
                    (existing, replacement) -> existing));

            sceneRuleDOPageResult.getList().forEach(item -> {
                SceneRulePageRespVO sceneRulePageRespVO = BeanUtils.toBean(item, SceneRulePageRespVO.class);
                Long loginUserId = Long.valueOf(item.getCreator());
                AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
                sceneRulePageRespVO.setCreatorName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
                if (spaceMap.containsKey(item.getResourceSpaceId())) {
                    sceneRulePageRespVO.setSpaceName(spaceMap.get(item.getResourceSpaceId()));
                }
                pageRuleList.add(sceneRulePageRespVO);
            });
        }
        return new PageResult<>(pageRuleList, sceneRuleDOPageResult.getTotal());
    }

    @Override
    public List<SceneRuleDO> getSceneRuleByState(Integer state) {
        return sceneRuleMapper.selectList(new LambdaQueryWrapperX<SceneRuleDO>()
                .eq(SceneRuleDO::getStatus, state));
    }

    @Override
    public void updateUnsatisfiedTimes(Long id, Integer unsatisfiedTimes) {
        // 更新
        SceneRuleDO updateObj = new SceneRuleDO();
        updateObj.setId(id);
        updateObj.setUnsatisfiedTimes(unsatisfiedTimes);
        sceneRuleMapper.updateById(updateObj);
    }

    /**
     * 设备删除，场景失效
     * @param ruleList
     */
    public void invalidSceneRule(List<Long> ruleList){

        ruleList.forEach(item -> {
            SceneRuleDO sceneRuleDO = new SceneRuleDO();
            sceneRuleDO.setId(item);
            sceneRuleDO.setStatus(2);
            sceneRuleMapper.updateById(sceneRuleDO);
        });
    }


}