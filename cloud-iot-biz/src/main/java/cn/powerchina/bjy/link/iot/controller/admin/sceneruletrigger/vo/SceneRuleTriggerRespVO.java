package cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 规则触发/条件限制 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneRuleTriggerRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8284")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17332")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "条件类型:1-触发条件,2-限制条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("条件类型:1-触发条件,2-限制条件")
    private Integer conditionType;

    @Schema(description = "触发类型:1-定时触发,2-设备触发", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("触发类型:1-定时触发,2-设备触发")
    private Integer triggerType;

    @Schema(description = "设备触发方式:1-属性触发,2-事件触发,3-上下线触发", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("设备触发方式:1-属性触发,2-事件触发,3-上下线触发")
    private Integer deviceTriggerType;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品code")
    private String productCode;

    @Schema(description = "设备code,为空表示所有设备")
    @ExcelProperty("设备code,为空表示所有设备")
    private String deviceCode;

    @Schema(description = "属性表达式")
    @ExcelProperty("属性表达式")
    private String attributeExpression;

    @Schema(description = "事件唯一标识")
    @ExcelProperty("事件唯一标识")
    private String eventIdentity;

    @Schema(description = "上下线状态:1-上线,2-下线", example = "2")
    @ExcelProperty("上下线状态:1-上线,2-下线")
    private Integer onlineStatus;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否失效 1失效,0未失效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否失效 1失效,0未失效")
    private Integer isInvalid;

}
