package cn.powerchina.bjy.link.iot.service.appauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.appauth.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.appauth.AppAuthDO;
import cn.powerchina.bjy.link.iot.dal.mysql.appauth.AppAuthMapper;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.UUID;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.APP_AUTH_NOT_EXISTS;


/**
 * 应用管理认证 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppAuthServiceImpl implements AppAuthService {

    @Resource
    private AppAuthMapper appAuthMapper;

    @Override
    public Long createAppAuth(AppAuthCreateReqVO createReqVO) {
        // 插入
        AppAuthDO appAuth = BeanUtils.toBean(createReqVO, AppAuthDO.class);
        appAuth.setAppCode(UUID.randomUUID().toString().replaceAll("-", ""));
        appAuth.setSecret(UUID.randomUUID().toString().replaceAll("-", ""));
        appAuth.setStatus(EnableStateEnum.YES.getType());
        appAuthMapper.insert(appAuth);
        // 返回
        return appAuth.getId();
    }

    @Override
    public void updateAppAuth(AppAuthUpdateReqVO updateReqVO) {
        // 校验存在
        validateAppAuthExists(updateReqVO.getId());
        // 更新
        AppAuthDO updateObj = BeanUtils.toBean(updateReqVO, AppAuthDO.class);
        appAuthMapper.updateById(updateObj);
    }

    @Override
    public void deleteAppAuth(Long id) {
        // 校验存在
        validateAppAuthExists(id);
        // 删除
        appAuthMapper.deleteById(id);
    }

    private void validateAppAuthExists(Long id) {
        if (appAuthMapper.selectById(id) == null) {
            throw exception(APP_AUTH_NOT_EXISTS);
        }
    }

    @Override
    public AppAuthDO getAppAuth(Long id) {
        return appAuthMapper.selectById(id);
    }

    @Override
    public PageResult<AppAuthDO> getAppAuthPage(AppAuthPageReqVO pageReqVO) {
        return appAuthMapper.selectPage(pageReqVO);
    }

    @Override
    public void switchStatus(AppAuthSwitchReqVO appAuthSwitchReqVO) {
        validateAppAuthExists(appAuthSwitchReqVO.getId());
        AppAuthDO updateObj = BeanUtils.toBean(appAuthSwitchReqVO, AppAuthDO.class);
        appAuthMapper.updateById(updateObj);
    }

    @Override
    public void resetSecret(Long id) {
        validateAppAuthExists(id);
        AppAuthDO updateObj = new AppAuthDO();
        updateObj.setId(id);
        updateObj.setSecret(UUID.randomUUID().toString().replaceAll("-", ""));
        appAuthMapper.updateById(updateObj);
    }

}