package cn.powerchina.bjy.link.iot.listener.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dal.tdengine.IotDevicePropertyMapper;
import cn.powerchina.bjy.link.iot.dto.message.DeviceAttributeModel;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.framework.rule.RuleActionService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * @Description: 监听设备上报的消息
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_DATA, consumerGroup = IotTopicConstant.GROUP_DEVICE_PROPERTY)
public class DeviceDataReceiver implements RocketMQListener {


    @Resource
    private RuleActionService ruleActionService;

    @Resource
    private IotDevicePropertyMapper devicePropertyMapper;

    @Resource
    private DeviceShadowService deviceShadowService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        // 解析消息体
        DeviceAttributeModel deviceAttributeModel = parseMessageBody(messageView);
        if (deviceAttributeModel == null) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        log.info("receive message: {}", JSONObject.toJSON(deviceAttributeModel));

        //变化速率：上一次最新的值
        Map<String, Object> lastValueMap = new HashMap<>();
        insertReportData(deviceAttributeModel, lastValueMap);
        try {
            Facts facts = new Facts();
            facts.put("productCode", deviceAttributeModel.getProductCode());
            facts.put("deviceCode", deviceAttributeModel.getDeviceCode());
            if (CollectionUtil.isNotEmpty(deviceAttributeModel.getData())) {
                deviceAttributeModel.getData().forEach(facts::put);
            }
            if (CollectionUtil.isNotEmpty(lastValueMap)) {
                lastValueMap.forEach(facts::put);
            }
            ruleActionService.matchRuleAndAction(facts);
        } catch (Exception e) {
            log.error("根据上报的属性，匹配规则异常：{}", e.getMessage());
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 时序数据库保存属性
     * mysql更新最新的属性值（影子）
     *
     * @param deviceAttributeModel
     * @param lastValueMap
     */
    private void insertReportData(DeviceAttributeModel deviceAttributeModel, Map<String, Object> lastValueMap) {
        try {
            devicePropertyMapper.insert(deviceAttributeModel.getProductCode(), deviceAttributeModel.getDeviceCode(), deviceAttributeModel.getData(), deviceAttributeModel.getCurrentTime());
        } catch (Exception e) {
            log.error("时序数据库持久化属性异常 {}", e.getMessage());
            throw exception(PRODUCT_MODEL_TD_INSERT_DATA);
        }
        try {
            List<DeviceShadowDO> currentDBList = deviceShadowService.getShadowListByDeviceCode(deviceAttributeModel.getDeviceCode());
            Map<String, DeviceShadowDO> identityMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(currentDBList)) {
                identityMap = currentDBList.stream().collect(Collectors.toMap(DeviceShadowDO::getThingIdentity, Function.identity()));
            }
            List<DeviceShadowDO> newAttributeList = new ArrayList<>();
            List<DeviceShadowDO> updateAttributeList = new ArrayList<>();
            Map<String, DeviceShadowDO> finalIdentityMap = identityMap;
            LocalDateTime reportTime = deviceAttributeModel.getCurrentTime() == null ? LocalDateTime.now() : Instant.ofEpochMilli(deviceAttributeModel.getCurrentTime())
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            deviceAttributeModel.getData().forEach((key, value) -> {
                if (value != null) {
                    if (finalIdentityMap.containsKey(key)) {
                        DeviceShadowDO currentAttributeDO = finalIdentityMap.get(key);
                        lastValueMap.put("last_" + key, currentAttributeDO.getThingValue());
                        currentAttributeDO.setReportTime(reportTime);
                        currentAttributeDO.setThingIdentity(key);
                        currentAttributeDO.setThingValue(String.valueOf(value));
                        currentAttributeDO.setUpdateTime(LocalDateTime.now());
                        updateAttributeList.add(currentAttributeDO);
                    } else {
                        DeviceShadowDO currentAttributeDO = new DeviceShadowDO();
                        currentAttributeDO.setDeviceCode(deviceAttributeModel.getDeviceCode());
                        currentAttributeDO.setProductCode(deviceAttributeModel.getProductCode());
                        currentAttributeDO.setReportTime(reportTime);
                        currentAttributeDO.setThingIdentity(key);
                        currentAttributeDO.setThingValue(String.valueOf(value));
                        newAttributeList.add(currentAttributeDO);
                    }
                }
            });
            deviceShadowService.createShadow(newAttributeList);
            deviceShadowService.updateShadow(updateAttributeList);
        } catch (Exception e) {
            log.error("持久化最新属性消息 {} 异常 {}", deviceAttributeModel.getMessageId(), e.getMessage());
            //throw exception(DEVICE_SHADOW_UPDATE);
        }
    }

    /**
     * 解析消息体为实体类
     */
    private DeviceAttributeModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceAttributeModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }

}
