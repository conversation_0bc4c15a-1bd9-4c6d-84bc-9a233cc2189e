package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.link.iot.controller.admin.command.vo.EdgeCommandReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.enums.CommandServeEnum;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.model.DeviceCommandModel;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.edgecommand.EdgeCommandService;
import cn.powerchina.bjy.link.iot.service.properties.PropertiesService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * @Description: 监听大坝下发的时钟同步指令
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DAM_CLOCK_SYNC_COMMAND, consumerGroup =IotTopicConstant.GROUP_DAM_CLOCK_SYNC_COMMAND )
public class DamClockSyncReceiver implements RocketMQListener {

    @Autowired
    private EdgeCommandService edgeCommandService;

    @Autowired
    private DeviceService deviceService;


    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            DeviceCommandModel deviceCommandModel = parseMessageBody(messageView);
            if (deviceCommandModel == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            log.info("receive dam command: {}", JSONObject.toJSON(deviceCommandModel));
            DeviceDO deviceDO = deviceService.getDeviceByCode(deviceCommandModel.getDeviceCode());

            EdgeCommandReqVO edgeCommandReqVO = new EdgeCommandReqVO();
            edgeCommandReqVO.setDeviceCode(deviceCommandModel.getDeviceCode());
            edgeCommandReqVO.setInputParams("[]");
            edgeCommandReqVO.setOperatorSource(0);
            edgeCommandReqVO.setProductCode(deviceDO.getProductCode());
            edgeCommandReqVO.setThingIdentity(CommandServeEnum.SET_DATETIME.getThingIdentity());
            edgeCommandReqVO.setThingName("设置时间");
            edgeCommandReqVO.setThingType(2);
            edgeCommandService.commandDown(edgeCommandReqVO);
        } catch (Exception e) {
            log.error("edgeOnlineCheckReceive--->error,msg={}", e.getMessage(), e);
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private DeviceCommandModel parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, DeviceCommandModel.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
