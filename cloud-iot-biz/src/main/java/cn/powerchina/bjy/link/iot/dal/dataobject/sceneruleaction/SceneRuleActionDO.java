package cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 场景规则执行动作 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_rule_action")
@KeySequence("iot_scene_rule_action_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneRuleActionDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 动作类型:1-设备动作,2-告警动作,3-执行场景
     */
    private Integer actionType;
    /**
     * 延迟执行时间
     */
    private String delaySeconds;
    /**
     * 产品code
     */
    private String productCode;
    /**
     * 设备code,为空表示所有设备
     */
    private String deviceCode;
    /**
     * 指令配置
     */
    private String commandConfig;
    /**
     * 场景ID：规则id
     */
    private Long sceneId;
    /**
     * 场景状态:1-执行场景,2-开启场景,3-禁用场景
     */
    private Integer sceneStatus;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否失效 1失效,0未失效
     */
    private Integer isInvalid;

}