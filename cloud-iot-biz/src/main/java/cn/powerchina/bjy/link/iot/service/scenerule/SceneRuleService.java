package cn.powerchina.bjy.link.iot.service.scenerule;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.RuleSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRuleSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import jakarta.validation.*;

import java.util.List;

/**
 * 场景规则 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneRuleService {

    /**
     * 切换场景规则状态
     *
     * @param id 编号
     * @param status 状态:0-禁用,1-启用
     */
    void switchSceneRuleStatus(Long id, Integer status);

    /**
     * 创建场景规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSceneRule(@Valid SceneRuleSaveReqVO createReqVO);


    Long createAndUpdateSceneRule(@Valid RuleSaveReqVO ruleSaveReqVO);

    /**
     * 更新场景规则
     *
     * @param updateReqVO 更新信息
     */
    void updateSceneRule(@Valid SceneRuleSaveReqVO updateReqVO);

    /**
     * 删除场景规则
     *
     * @param id 编号
     */
    void deleteSceneRule(Long id);

    /**
     * 获得场景规则
     *
     * @param id 编号
     * @return 场景规则
     */
    SceneRuleDO getSceneRule(Long id);

    /**
     * 返回规则触发器和执行动作
     *
     * @param id
     * @return
     */
    RuleSaveReqVO getSceneRuleTriggerAndAction(Long id);

    /**
     * 获得场景规则分页
     *
     * @param pageReqVO 分页查询
     * @return 场景规则分页
     */
    PageResult<SceneRulePageRespVO> getSceneRulePage(SceneRulePageReqVO pageReqVO);

    /**
     * 根据状态查询规则
     *
     * @param state
     * @return
     */
    List<SceneRuleDO> getSceneRuleByState(Integer state);

    /**
     * 更新规则未触发次数
     *
     * @param id
     */
    void updateUnsatisfiedTimes(Long id, Integer unsatisfiedTimes);

    /**
     * 设备删除，场景失效
     * @param ruleList
     */
    void invalidSceneRule(List<Long> ruleList);
}