package cn.powerchina.bjy.link.iot.controller.admin.appauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.appauth.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.appauth.AppAuthDO;
import cn.powerchina.bjy.link.iot.service.appauth.AppAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 应用管理认证")
@RestController
@RequestMapping("/iot/app-auth")
@Validated
public class AppAuthController {

    @Resource
    private AppAuthService appAuthService;

    @PostMapping("/create")
    @Operation(summary = "创建应用管理认证")
    @PreAuthorize("@ss.hasPermission('iot:app-auth:create')")
    public CommonResult<Long> createAppAuth(@Valid @RequestBody AppAuthCreateReqVO createReqVO) {
        return success(appAuthService.createAppAuth(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新应用管理认证")
    @PreAuthorize("@ss.hasPermission('iot:app-auth:update')")
    public CommonResult<Boolean> updateAppAuth(@Valid @RequestBody AppAuthUpdateReqVO updateReqVO) {
        appAuthService.updateAppAuth(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除应用管理认证")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:app-auth:delete')")
    public CommonResult<Boolean> deleteAppAuth(@RequestParam("id") Long id) {
        appAuthService.deleteAppAuth(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得应用管理认证")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:app-auth:query')")
    public CommonResult<AppAuthRespVO> getAppAuth(@RequestParam("id") Long id) {
        AppAuthDO appAuth = appAuthService.getAppAuth(id);
        return success(BeanUtils.toBean(appAuth, AppAuthRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得应用管理认证分页")
    @PreAuthorize("@ss.hasPermission('iot:app-auth:query')")
    public CommonResult<PageResult<AppAuthRespVO>> getAppAuthPage(@Valid AppAuthPageReqVO pageReqVO) {
        PageResult<AppAuthDO> pageResult = appAuthService.getAppAuthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppAuthRespVO.class));
    }

    @PutMapping("/switch")
    @Operation(summary = "切换启用状态")
    @PreAuthorize("@ss.hasPermission('iot:app-auth:update')")
    public CommonResult<Boolean> switchAppAuth(@Valid @RequestBody AppAuthSwitchReqVO switchReqVO) {
        appAuthService.switchStatus(switchReqVO);
        return success(true);
    }

    @PutMapping("/secret")
    @Operation(summary = "重置密钥")
    @PreAuthorize("@ss.hasPermission('iot:app-auth:update')")
    public CommonResult<Boolean> resetSecret(@Valid @RequestBody AppAuthResetSecretReqVO resetSecretReqVO) {
        appAuthService.resetSecret(resetSecretReqVO.getId());
        return success(true);
    }
}