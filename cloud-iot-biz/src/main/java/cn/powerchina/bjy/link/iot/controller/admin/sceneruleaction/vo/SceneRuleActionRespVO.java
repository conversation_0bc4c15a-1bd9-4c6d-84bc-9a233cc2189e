package cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 场景规则执行动作 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneRuleActionRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12608")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23926")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "动作类型:1-设备动作,2-告警动作,3-执行场景", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("动作类型:1-设备动作,2-告警动作,3-执行场景")
    private Integer actionType;

    @Schema(description = "指令类型:1-属性,2-服务", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("指令类型:1-属性,2-服务")
    private Integer commandType;

    @Schema(description = "延迟执行时间(单位秒)")
    @ExcelProperty("延迟执行时间(单位秒)")
    private String delaySeconds;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品code")
    private String productCode;

    @Schema(description = "设备code,为空表示所有设备")
    @ExcelProperty("设备code,为空表示所有设备")
    private String deviceCode;

    @Schema(description = "指令唯一标识：属性/服务")
    @ExcelProperty("指令唯一标识：属性/服务")
    private String commandIdentity;

    @Schema(description = "参数配置")
    @ExcelProperty("参数配置")
    private String paramsValue;

    @Schema(description = "场景ID：规则id", example = "12646")
    @ExcelProperty("场景ID：规则id")
    private Long sceneId;

    @Schema(description = "场景状态:1-执行场景,2-开启场景,3-禁用场景", example = "1")
    @ExcelProperty("场景状态:1-执行场景,2-开启场景,3-禁用场景")
    private Integer sceneStatus;

    @Schema(description = "告警模板id", example = "7419")
    @ExcelProperty("告警模板id")
    private Long alarmTemplateId;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否失效 1失效,0未失效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否失效 1失效,0未失效")
    private Integer isInvalid;

}