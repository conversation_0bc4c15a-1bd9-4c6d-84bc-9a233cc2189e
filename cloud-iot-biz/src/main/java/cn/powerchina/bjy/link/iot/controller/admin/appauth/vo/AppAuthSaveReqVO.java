package cn.powerchina.bjy.link.iot.controller.admin.appauth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 应用管理认证新增/修改 Request VO")
@Data
public class AppAuthSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1323")
    private Long id;

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "应用名称不能为空")
    private String appName;

    @Schema(description = "应用code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "应用code不能为空")
    private String appCode;

    @Schema(description = "密钥")
    private String secret;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "应用状态(1:启用;0:禁用)")
    private Integer status;

}