package cn.powerchina.bjy.link.iot.service.datapermissions;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.resourcespace.ResourceSpaceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 数据权限 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DataPermissionsServiceImpl implements DataPermissionsService {

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Resource
    private ResourceSpaceMapper resourceSpaceMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private EdgeGatewayMapper edgeGatewayMapper;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private SceneRuleTriggerService sceneRuleTriggerService;
    @Resource
    private SceneRuleActionService sceneRuleActionService;
    @Resource
    private SceneRuleService sceneRuleService;

    @Override
    @Transactional
    public Boolean createDataPermissions(DataPermissionsSaveReqVO createReqVO) {
        //删除角色跟权限关联关系
        roleDataPermissionsMapper.deleteByRoleId(Long.parseLong(createReqVO.getRoleId()));
        //添加角色与权限关联关系信息
        RoleDataPermissionsDO roleDataPermissionsDO = BeanUtils.toBean(createReqVO, RoleDataPermissionsDO.class);
        roleDataPermissionsMapper.insert(roleDataPermissionsDO);

        dataPermissionsMapper.deleteByRoleId(Long.parseLong(createReqVO.getRoleId()));
        if (!CollectionUtils.isAnyEmpty(createReqVO.getDataScopeDeptIds())) {
            List<DataPermissionsDO> list = addDataPermissions(createReqVO);
            if (!CollectionUtils.isAnyEmpty(list)) {
                dataPermissionsMapper.insertBatch(list);
            }

            //场景失效
            CommonResult<Set<Long>> users = permissionApi.getUserRoleIdListByRoleIds(Collections.singletonList(Long.parseLong(createReqVO.getRoleId())));
            if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(users.getCode())) {
                for (Long userId : users.getData()) {
                    //查询人员创建的触发条件、限制条件
                    List<Long> sceneRuleTriggerList = sceneRuleTriggerService.invalidSceneRuleTrigger(userId, list);
                    //执行动作失效
                    List<Long> sceneRuleActionList = sceneRuleActionService.invalidSceneRuleAction(userId, list);
                    //场景联动失效
                    List<Long> allRuleList = new ArrayList<>();
                    allRuleList.addAll(sceneRuleTriggerList);
                    allRuleList.addAll(sceneRuleActionList);
                    List<Long> ruleList = allRuleList.stream().distinct().collect(Collectors.toList());
                    sceneRuleService.invalidSceneRule(ruleList);
                }
            }


        }
        return true;
    }

    @Override
    public Boolean updateDataPermissions(DataPermissionsSaveReqVO updateReqVO) {
        //删除当前用户的权限数据
        dataPermissionsMapper.deleteByRoleId(getLoginUserId());
        return dataPermissionsMapper.insertBatch(addDataPermissions(updateReqVO));
    }

    @Override
    public void deleteDataPermissions(Long id) {
        // 校验存在
        validateDataPermissionsExists(id);
        // 删除
        dataPermissionsMapper.deleteById(id);
    }

    private void validateDataPermissionsExists(Long id) {
        if (dataPermissionsMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.DATA_PERMISSIONS_NOT_EXISTS);
        }
    }

//    @Override
//    public DataPermissionsSaveReqVO getDataPermissions() {
//        DataPermissionsSaveReqVO resp=new DataPermissionsSaveReqVO();
//        List<DataPermissionsDO> list=dataPermissionsMapper.selectListByRoleId(getLoginUserId());
//        List<DataPermissionsDO>  doList=dataPermissionsMapper.selectList(getLoginUserId());
//        if(!CollectionUtils.isAnyEmpty(list))
//        {
//            List<DataResourceSaveRespVO> resourceSaveRespVOList=new ArrayList<>();
//            list.stream().forEach(resoure -> {
//                DataResourceSaveRespVO dataResourceSaveRespVO=new DataResourceSaveRespVO();
//                dataResourceSaveRespVO.setResourceSpaceId(resoure.getResourceSpaceId()+"");
//                if(DataPermissionsEnum.PRODUCT.getCode().equals(resoure.getType()))
//                {
//                    List<DataProductSaveRespVO> dataProductSaveRespVOList=new ArrayList<>();
//                    DataProductSaveRespVO dataProductSaveRespVO=new DataProductSaveRespVO();
//                    dataProductSaveRespVO.setProductId(resoure.getProductOrEdgeId());
//                    List<String> deviceIds=new ArrayList<>();
//                    doList.stream().forEach(devices ->{
//                        if(devices.getProductOrEdgeId().equals(resoure.getProductOrEdgeId()))
//                        {
//                            deviceIds.add(devices.getDeviceOrGatewayId());
//                        }
//                    });
//                    dataProductSaveRespVO.setDeviceIds(deviceIds);
//                    dataProductSaveRespVOList.add(dataProductSaveRespVO);
//                    dataResourceSaveRespVO.setProductList(dataProductSaveRespVOList);
//                }
//                if(DataPermissionsEnum.GATEWAY.getCode().equals(resoure.getType()))
//                {
//                    List<DataGatewaySaveRespVO> dataGatewaySaveRespVOList=new ArrayList<>();
//                    DataGatewaySaveRespVO dataGatewaySaveRespVO=new DataGatewaySaveRespVO();
//                    dataGatewaySaveRespVO.setEdgeId(resoure.getProductOrEdgeId());
//                    List<String> edgeIds=new ArrayList<>();
//                    doList.stream().forEach(edges ->{
//                        if(edges.getDeviceOrGatewayId().equals(resoure.getDeviceOrGatewayId()))
//                        {
//                            edgeIds.add(edges.getDeviceOrGatewayId());
//                        }
//                    });
//                    dataGatewaySaveRespVO.setGatewayIds(edgeIds);
//                    dataGatewaySaveRespVOList.add(dataGatewaySaveRespVO);
//                    dataResourceSaveRespVO.setEdgeList(dataGatewaySaveRespVOList);
//                }
//                resourceSaveRespVOList.add(dataResourceSaveRespVO);
//            });
////            resp.setRespVOList(resourceSaveRespVOList);
//            resp.setRoleId(getLoginUserId()+"");
//        }
//
//        return resp;
//    }

    @Override
    public PageResult<DataPermissionsDO> getDataPermissionsPage(DataPermissionsPageReqVO pageReqVO) {
        return dataPermissionsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DataPermissionsTreeVO> getAllList() {

        //查询所有资源空间数据
        List<ResourceSpaceDO> resourceSpaceDOList = resourceSpaceMapper.selectList();
        //查询所有产品数据
        List<ProductDO> productDOList = productMapper.selectList();
        //查询所有设备数据
        List<DeviceDO> deviceDOList = deviceMapper.selectList();
        //查询所有边缘实例数据
        List<EdgeGatewayDO> edgeGatewayDOList = edgeGatewayMapper.selectList();
        List<DataPermissionsTreeVO> list = new ArrayList<>();
        if (!CollectionUtils.isAnyEmpty(resourceSpaceDOList)) {
            resourceSpaceDOList.stream().forEach(resoure -> {
                list.add(DataPermissionsTreeVO.builder().id(resoure.getId() + "").name(resoure.getSpaceName()).parentId("0").level(1).build());
                list.add(DataPermissionsTreeVO.builder().id(-resoure.getId() + "").name("(+)所有产品").parentId(resoure.getId().toString()).level(2).build());
                productDOList.stream().forEach(product -> {
                    if (resoure.getId().equals(product.getResourceSpaceId())) {
                        list.add(DataPermissionsTreeVO.builder().id(product.getId() + "").name(product.getProductName()).parentId(resoure.getId().toString()).level(3).build());
                        list.add(DataPermissionsTreeVO.builder().id(-product.getId() + "").name("(+)所有设备").parentId(product.getId().toString()).level(4).build());
                        deviceDOList.forEach(device -> {
                            if (product.getProductCode().equals(device.getProductCode())) {
                                list.add(DataPermissionsTreeVO.builder().id(device.getId() + "").name(device.getDeviceName()).parentId(product.getId().toString()).level(5).build());
                            }
                        });
                    }
                });
                Long gateWayId = resoure.getId() + 10;
                list.add(DataPermissionsTreeVO.builder().id(-gateWayId + "").name("边缘计算").parentId(resoure.getId().toString()).level(6).build());
                list.add(DataPermissionsTreeVO.builder().id(-gateWayId + 100 + "").name("(+)所有边缘实例").parentId(-gateWayId + "").level(7).build());
                edgeGatewayDOList.stream().forEach(gateway -> {
                    if (gateway.getResourceSpaceId() != null && resoure.getId().equals(gateway.getResourceSpaceId())) {
                        list.add(DataPermissionsTreeVO.builder().id(gateway.getId() + "").name(gateway.getEdgeName()).parentId(-gateWayId + "").level(8).build());
                    }
                });
            });
        }
        return list;
    }

    @Override
    public DataPermissionsSaveReqVO getAllListByRoleId(String roleId) {

        DataPermissionsSaveReqVO dataPermissionsSaveReqVO = new DataPermissionsSaveReqVO();
        List<DataPermissionsTreeVO> dataList = new ArrayList<>();
        RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(Long.parseLong(roleId));
        if (roleDataPermissionsDO != null) {
            dataPermissionsSaveReqVO.setRoleId(roleDataPermissionsDO.getRoleId().toString());
            dataPermissionsSaveReqVO.setDataScope(roleDataPermissionsDO.getDataScope());
            List<Integer> levels = new ArrayList<>();
            levels.add(1);
            levels.add(3);
            levels.add(6);
            //List<DataPermissionsDO> list = dataPermissionsMapper.selectAllByRoleId(Long.parseLong(roleId),levels);

            LambdaQueryWrapper<DataPermissionsDO> lambdaWrapper = new LambdaQueryWrapper<>();
            lambdaWrapper.eq(DataPermissionsDO::getRoleId, Long.parseLong(roleId));
            lambdaWrapper.notIn(DataPermissionsDO::getLevel, levels);
            List<DataPermissionsDO> list = dataPermissionsMapper.selectList(lambdaWrapper);

            if (!CollectionUtils.isAnyEmpty(list)) {
                list.stream().forEach(item -> {
                    DataPermissionsTreeVO dataPermissionsTreeVO = new DataPermissionsTreeVO();
                    dataPermissionsTreeVO.setId(item.getDataId());
                    dataPermissionsTreeVO.setParentId(item.getParentId());
                    dataPermissionsTreeVO.setLevel(item.getLevel());
                    dataPermissionsTreeVO.setName(item.getName());
                    dataList.add(dataPermissionsTreeVO);
                });
            }
            dataPermissionsSaveReqVO.setDataScopeDeptIds(dataList);
        }
        return dataPermissionsSaveReqVO;
    }

    private List<DataPermissionsDO> addDataPermissions(DataPermissionsSaveReqVO reqVO) {
        // 插入
        List<DataPermissionsDO> list = new ArrayList<>();
        reqVO.getDataScopeDeptIds().stream().forEach(item -> {

            DataPermissionsDO dataPermissionsDO = new DataPermissionsDO();
            dataPermissionsDO.setDataId(item.getId());
            dataPermissionsDO.setName(item.getName());
            dataPermissionsDO.setRoleId(Long.parseLong(reqVO.getRoleId()));
            dataPermissionsDO.setParentId(item.getParentId().toString());
            dataPermissionsDO.setLevel(item.getLevel());
            list.add(dataPermissionsDO);
        });
        return list;
    }
}