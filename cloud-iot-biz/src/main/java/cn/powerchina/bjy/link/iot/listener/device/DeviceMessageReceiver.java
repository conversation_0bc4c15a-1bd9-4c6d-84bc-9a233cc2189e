package cn.powerchina.bjy.link.iot.listener.device;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dto.message.DevicePropertiesReportModel;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * @Description: 监听设备上报的消息
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotDeviceMessage.TOPIC_IOT_DEVICE_MESSAGE, consumerGroup = IotDeviceMessage.GROUP_IOT_DEVICE_MESSAGE)
public class DeviceMessageReceiver implements RocketMQListener {


    @Resource
    private DevicePropertiesHandler devicePropertiesHandler;

    @Resource
    private DeviceService deviceService;

    @Resource
    private ThreadPoolTaskExecutor iotThreadPoolTaskExecutor;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        // 解析消息体
        IotDeviceMessage iotDeviceMessage = parseMessageBody(messageView);
        if (iotDeviceMessage == null) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        //设备是否存在
        DeviceDO device = deviceService.getDevice(iotDeviceMessage.getDeviceCode());
        if (device == null) {
            log.error("设备 {} 不存在，丢弃消息 {}", iotDeviceMessage.getDeviceCode(), iotDeviceMessage.getId());
            return ConsumeResult.SUCCESS;
        }

        log.info("receive message: {}", JSONObject.toJSON(iotDeviceMessage));
        IotDeviceMessageMethodEnum enumByMethod = IotDeviceMessageMethodEnum.getEnumByMethod(iotDeviceMessage.getMethod());
        if (null == enumByMethod) {
            log.error("不支持的上报类型 {}", iotDeviceMessage.getMethod());
            return ConsumeResult.SUCCESS;
        }

        iotThreadPoolTaskExecutor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    //todo
                    switch (enumByMethod) {
                        case PROPERTIES_REPORT:
                            DevicePropertiesReportModel devicePropertiesReportModel = BeanUtils.toBean(iotDeviceMessage, DevicePropertiesReportModel.class);
                            devicePropertiesReportModel.setProductCode(device.getProductCode());
                            devicePropertiesHandler.handler(devicePropertiesReportModel);
                            break;

                    }
                } catch (Exception e) {
                    log.error("处理上行消息method={}异常{}", iotDeviceMessage.getMethod(), e.getMessage());
                }
            }
        });
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private IotDeviceMessage parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, IotDeviceMessage.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }

}
