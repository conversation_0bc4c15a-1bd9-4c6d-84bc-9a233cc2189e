package cn.powerchina.bjy.link.iot.controller.admin.appauth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 应用管理认证新增 Request VO")
@Data
public class AppAuthCreateReqVO {

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "应用名称不能为空")
    private String appName;

    @Schema(description = "描述")
    private String description;

}