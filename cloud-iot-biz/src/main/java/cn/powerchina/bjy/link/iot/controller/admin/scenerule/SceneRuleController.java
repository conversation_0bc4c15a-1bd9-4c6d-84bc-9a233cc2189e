package cn.powerchina.bjy.link.iot.controller.admin.scenerule;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.RuleSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRuleStateReqVO;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 场景规则")
@RestController
@RequestMapping("/iot/scene-rule")
@Validated
public class SceneRuleController {

    @Resource
    private SceneRuleService sceneRuleService;

    @PostMapping("/create")
    @Operation(summary = "创建/修改场景规则")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule:create')")
    public CommonResult<Long> createSceneRule(@Valid @RequestBody RuleSaveReqVO ruleSaveReqVO) {
        return success(sceneRuleService.createAndUpdateSceneRule(ruleSaveReqVO));
    }

    @PutMapping("/switch-status")
    @Operation(summary = "切换场景规则状态")
    // @PreAuthorize("@ss.hasPermission('iot:scene-rule:update')")
    public CommonResult<Boolean> switchSceneRuleStatus(@Valid @RequestBody SceneRuleStateReqVO reqVO) {
        sceneRuleService.switchSceneRuleStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除场景规则")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule:delete')")
    public CommonResult<Boolean> deleteSceneRule(@RequestParam("id") Long id) {
        sceneRuleService.deleteSceneRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得场景规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule:query')")
    public CommonResult<RuleSaveReqVO> getSceneRule(@RequestParam("id") Long id) {
        RuleSaveReqVO sceneRule = sceneRuleService.getSceneRuleTriggerAndAction(id);
        return success(BeanUtils.toBean(sceneRule, RuleSaveReqVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得场景规则分页")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule:query')")
    public CommonResult<PageResult<SceneRulePageRespVO>> getSceneRulePage(@Valid SceneRulePageReqVO pageReqVO) {
        PageResult<SceneRulePageRespVO> pageResult = sceneRuleService.getSceneRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SceneRulePageRespVO.class));
    }

}