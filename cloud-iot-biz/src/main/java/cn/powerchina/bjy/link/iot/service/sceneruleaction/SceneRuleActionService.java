package cn.powerchina.bjy.link.iot.service.sceneruleaction;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import jakarta.validation.*;

import java.util.List;

/**
 * 场景规则执行动作 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneRuleActionService {

    void createAndUpdateSceneRuleAction(List<SceneRuleActionSaveReqVO> saveOrUpdateList, Long ruleId);

    List<SceneRuleActionDO> getSceneRuleActionByRuleId(Long ruleId);

    /**
     * 根据场景id查询执行动作列表
     *
     * @param sceneId
     * @return
     */
    List<SceneRuleActionDO> getRuleActionBySceneId(Long sceneId);

    /**
     * 创建场景规则执行动作
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSceneRuleAction(@Valid SceneRuleActionSaveReqVO createReqVO);

    /**
     * 更新场景规则执行动作
     *
     * @param updateReqVO 更新信息
     */
    void updateSceneRuleAction(@Valid SceneRuleActionSaveReqVO updateReqVO);

    /**
     * 删除场景规则执行动作
     *
     * @param id 编号
     */
    void deleteSceneRuleAction(Long id);

    /**
     * 获得场景规则执行动作
     *
     * @param id 编号
     * @return 场景规则执行动作
     */
    SceneRuleActionDO getSceneRuleAction(Long id);

    /**
     * 获得场景规则执行动作分页
     *
     * @param pageReqVO 分页查询
     * @return 场景规则执行动作分页
     */
    PageResult<SceneRuleActionDO> getSceneRuleActionPage(SceneRuleActionPageReqVO pageReqVO);

    /**
     * 执行动作删除
     * @param deviceCode
     * @return
     */
    List<Long> deleteSceneRuleAction(String deviceCode);

    /**
     * 执行动作失效
     * @param userId
     * @param dataPermissionsList
     * @return
     */
    List<Long> invalidSceneRuleAction(Long userId, List<DataPermissionsDO> dataPermissionsList);

}