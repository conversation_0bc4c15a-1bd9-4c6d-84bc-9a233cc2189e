package cn.powerchina.bjy.link.iot.controller.admin.appauth.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 应用管理认证 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppAuthRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1323")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "应用名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("应用名称")
    private String appName;

    @Schema(description = "应用code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("应用code")
    private String appCode;

    @Schema(description = "密钥")
    @ExcelProperty("密钥")
    private String secret;

    @Schema(description = "描述")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "应用状态：启用/禁用", example = "2")
    @ExcelProperty("应用状态：启用/禁用")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}