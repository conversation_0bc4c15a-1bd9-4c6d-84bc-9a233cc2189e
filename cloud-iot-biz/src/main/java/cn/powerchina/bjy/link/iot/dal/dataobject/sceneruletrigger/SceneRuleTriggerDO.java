package cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 规则触发/条件限制 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_rule_trigger")
@KeySequence("iot_scene_rule_trigger_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneRuleTriggerDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 条件类型:1-触发条件,2-限制条件
     */
    private Integer conditionType;
    /**
     * 触发类型:1-定时触发,2-设备触发
     */
    private Integer triggerType;
    /**
     * 设备触发方式:1-属性触发,2-事件触发,3-上下线触发
     */
    private Integer deviceTriggerType;
    /**
     * 产品code
     */
    private String productCode;
    /**
     * 设备code,为空表示所有设备
     */
    private String deviceCode;
    /**
     * 属性表达式
     */
    private String attributeExpression;

    /**
     * 属性唯一标识符
     */
    private String attributeIdentity;
    /**
     * 条件:1-大于,2-小于,3-等于,4-不等于,5-变化速率
     */
    private Integer attributeCondition;
    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 事件唯一标识
     */
    private String eventIdentity;
    /**
     * 上下线状态:1-上线,2-下线
     */
    private Integer onlineStatus;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否失效 1失效,0未失效
     */
    private Integer isInvalid;

}
