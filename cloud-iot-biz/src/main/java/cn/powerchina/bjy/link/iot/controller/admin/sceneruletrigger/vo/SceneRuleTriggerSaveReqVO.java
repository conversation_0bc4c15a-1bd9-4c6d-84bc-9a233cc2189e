package cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo;

import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerSaveReqVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 规则触发/条件限制新增/修改 Request VO")
@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class SceneRuleTriggerSaveReqVO {

    @Schema(description = "主键id",  example = "8284")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17332")
    private Long ruleId;

    @Schema(description = "条件类型:1-触发条件,2-限制条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "条件类型:1-触发条件,2-限制条件不能为空")
    private Integer conditionType;

    @Schema(description = "触发类型:1-定时触发,2-设备触发", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "触发类型:1-定时触发,2-设备触发不能为空")
    private Integer triggerType;

    @Schema(description = "设备触发方式:1-属性触发,2-事件触发,3-上下线触发", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer deviceTriggerType;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productCode;

    @Schema(description = "设备code,为空表示所有设备")
    private String deviceCode;

    @Schema(description = "属性表达式")
    private String attributeExpression;

    @Schema(description = "属性唯一标识符")
    private String attributeIdentity;

    @Schema(description = "条件:1-大于,2-小于,3-等于,4-不等于,5-变化速率")
    private Integer attributeCondition;

    @Schema(description = "属性值")
    private String attributeValue;

    @Schema(description = "事件唯一标识")
    private String eventIdentity;

    @Schema(description = "上下线状态:1-上线,2-下线", example = "2")
    private Integer onlineStatus;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

    @Schema(description = "定时触发")
    private SceneRuleTimeTriggerSaveReqVO ruleTimeTriggerSaveReqVO;

    @Schema(description = "是否失效 1失效,0未失效,2设备删除", example = "0")
    private Integer isInvalid;

}