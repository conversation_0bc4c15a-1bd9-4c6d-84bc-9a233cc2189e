package cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 规则触发/条件限制分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneRuleTriggerPageReqVO extends PageParam {

    @Schema(description = "规则ID", example = "17332")
    private Long ruleId;

    @Schema(description = "条件类型:1-触发条件,2-限制条件", example = "1")
    private Integer conditionType;

    @Schema(description = "触发类型:1-定时触发,2-设备触发", example = "2")
    private Integer triggerType;

    @Schema(description = "设备触发方式:1-属性触发,2-事件触发,3-上下线触发", example = "2")
    private Integer deviceTriggerType;

    @Schema(description = "产品code")
    private String productCode;

    @Schema(description = "设备code,为空表示所有设备")
    private String deviceCode;

    @Schema(description = "属性表达式")
    private String attributeExpression;

    @Schema(description = "事件唯一标识")
    private String eventIdentity;

    @Schema(description = "上下线状态:1-上线,2-下线", example = "2")
    private Integer onlineStatus;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "是否失效 1失效,0未失效", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer isInvalid;

}