# IoT平台系统泳道图

## 系统概述
本IoT平台是一个基于Spring Cloud微服务架构的物联网管理平台，主要包含设备接入、数据处理、规则引擎、权限管理等核心功能模块。

## 系统架构
- **cloud-iot-gateway**: 设备接入网关，支持HTTP、MQTT、TCP等多种协议
- **cloud-iot-biz**: 核心业务模块，包含设备管理、产品管理、规则引擎等
- **cloud-iot-api**: API接口定义模块，提供RPC服务接口

## 主要业务流程泳道图

```mermaid
graph TB
    subgraph "外部系统/设备"
        Device[IoT设备]
        WebUI[管理后台]
        ThirdParty[第三方系统]
    end

    subgraph "接入层 (cloud-iot-gateway)"
        Gateway[设备网关]
        HttpProtocol[HTTP协议处理器]
        MqttProtocol[MQTT协议处理器]
        TcpProtocol[TCP协议处理器]
        AuthHandler[认证处理器]
        TokenService[Token服务]
    end

    subgraph "消息中间件"
        RocketMQ[RocketMQ消息队列]
        Redis[Redis缓存]
    end

    subgraph "业务层 (cloud-iot-biz)"
        DeviceService[设备管理服务]
        ProductService[产品管理服务]
        RuleEngine[规则引擎]
        DataProcessor[数据处理器]
        MessageReceiver[消息接收器]
        AuthService[认证授权服务]
    end

    subgraph "数据层"
        MySQL[(MySQL数据库)]
        TDengine[(TDengine时序数据库)]
    end

    subgraph "外部服务"
        NacosRegistry[Nacos注册中心]
        NacosConfig[Nacos配置中心]
        SystemAPI[系统服务API]
    end

    %% 设备接入流程
    Device -->|1.设备连接| Gateway
    Gateway -->|2.协议解析| HttpProtocol
    Gateway -->|2.协议解析| MqttProtocol
    Gateway -->|2.协议解析| TcpProtocol
    
    HttpProtocol -->|3.设备认证| AuthHandler
    MqttProtocol -->|3.设备认证| AuthHandler
    TcpProtocol -->|3.设备认证| AuthHandler
    
    AuthHandler -->|4.Token生成| TokenService
    AuthHandler -->|5.认证验证| DeviceService
    
    %% 数据上报流程
    Gateway -->|6.消息发送| RocketMQ
    RocketMQ -->|7.消息消费| MessageReceiver
    MessageReceiver -->|8.数据处理| DataProcessor
    DataProcessor -->|9.规则匹配| RuleEngine
    DataProcessor -->|10.数据存储| TDengine
    
    %% 管理后台流程
    WebUI -->|11.管理操作| DeviceService
    WebUI -->|11.管理操作| ProductService
    WebUI -->|11.管理操作| AuthService
    
    DeviceService -->|12.数据查询| MySQL
    ProductService -->|12.数据查询| MySQL
    AuthService -->|13.权限验证| SystemAPI
    
    %% 缓存和配置
    DeviceService -.->|缓存| Redis
    ProductService -.->|缓存| Redis
    Gateway -.->|服务发现| NacosRegistry
    Gateway -.->|配置获取| NacosConfig
    
    %% 第三方集成
    ThirdParty -->|14.API调用| DeviceService
    RuleEngine -->|15.动作执行| ThirdParty

    %% 样式定义
    classDef deviceClass fill:#e1f5fe
    classDef gatewayClass fill:#f3e5f5
    classDef businessClass fill:#e8f5e8
    classDef dataClass fill:#fff3e0
    classDef externalClass fill:#fce4ec

    class Device,WebUI,ThirdParty deviceClass
    class Gateway,HttpProtocol,MqttProtocol,TcpProtocol,AuthHandler,TokenService gatewayClass
    class DeviceService,ProductService,RuleEngine,DataProcessor,MessageReceiver,AuthService businessClass
    class MySQL,TDengine,RocketMQ,Redis dataClass
    class NacosRegistry,NacosConfig,SystemAPI externalClass
```

## 详细业务流程说明

### 1. 设备接入认证流程
1. **设备连接**: IoT设备通过HTTP/MQTT/TCP协议连接到网关
2. **协议解析**: 网关根据协议类型分发到对应的处理器
3. **设备认证**: 验证设备的clientId、username、password
4. **Token生成**: 认证成功后生成JWT Token
5. **状态更新**: 设备上线状态更新

### 2. 数据上报处理流程
1. **消息接收**: 网关接收设备上报的数据
2. **消息解码**: 根据产品物模型解码消息
3. **消息队列**: 将消息发送到RocketMQ
4. **消息消费**: 业务层消费消息队列中的数据
5. **数据处理**: 处理设备属性、事件等数据
6. **规则匹配**: 根据场景规则进行匹配和动作执行
7. **数据存储**: 将数据存储到时序数据库

### 3. 管理后台操作流程
1. **用户登录**: 管理员通过Web界面登录
2. **权限验证**: 基于Spring Security进行权限验证
3. **业务操作**: 进行设备管理、产品管理等操作
4. **数据查询**: 从MySQL数据库查询业务数据
5. **结果返回**: 将操作结果返回给前端

### 4. 规则引擎流程
1. **规则配置**: 管理员配置场景规则
2. **数据触发**: 设备数据上报触发规则匹配
3. **条件判断**: 根据规则条件进行判断
4. **动作执行**: 执行相应的动作（告警、控制等）

## 核心技术栈
- **微服务框架**: Spring Cloud + Spring Boot
- **消息队列**: RocketMQ
- **数据库**: MySQL + TDengine
- **缓存**: Redis
- **注册中心**: Nacos
- **安全框架**: Spring Security + JWT
- **协议支持**: HTTP、MQTT、TCP
- **规则引擎**: Easy Rules

## 系统特点
1. **多协议支持**: 支持HTTP、MQTT、TCP等多种设备接入协议
2. **微服务架构**: 采用Spring Cloud微服务架构，支持水平扩展
3. **实时数据处理**: 基于消息队列的实时数据处理能力
4. **灵活规则引擎**: 支持自定义场景规则和动作执行
5. **完善权限管理**: 基于角色的细粒度权限控制
6. **高性能存储**: 采用时序数据库存储设备数据
