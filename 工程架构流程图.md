# 云链IoT平台工程架构流程图

## 项目概述

云链IoT平台是基于Spring Boot + Spring Cloud微服务架构构建的企业级物联网管理平台，支持多种设备接入协议，提供完整的设备管理、数据处理、规则引擎等核心功能。

## 模块架构

### 📡 cloud-iot-gateway (接入网关层)
- **功能**: 设备接入、协议适配、认证授权
- **支持协议**: HTTP、MQTT、TCP
- **核心组件**: 
  - 网关服务 (Gateway Service)
  - 认证过滤器 (Auth Filter)
  - 连接管理器 (Connection Manager)

### 💼 cloud-iot-biz (核心业务层)  
- **功能**: 业务逻辑处理、数据存储、规则引擎
- **主要服务**:
  - 设备管理服务 (DeviceService)
  - 产品管理服务 (ProductService)
  - 规则引擎 (RuleEngine)
  - 数据处理器 (DataProcessor)
- **消息处理**:
  - CommandReceiver: 命令接收器
  - MqttPublisher: MQTT消息发布器

### 🔌 cloud-iot-api (接口定义层)
- **功能**: RPC接口定义、Feign客户端
- **接口类型**:
  - 设备管理API
  - 产品管理API  
  - 认证授权API
  - 数据查询API

## 核心业务流程

### 1️⃣ 设备接入流程
```
IoT设备 → 网关协议适配 → 身份认证 → 连接管理 → 在线状态更新
```

### 2️⃣ 数据上报流程  
```
设备数据 → 网关接收 → RocketMQ消息队列 → 业务处理 → 规则匹配 → 数据存储
```

### 3️⃣ 指令下发流程
```
管理后台 → API接口 → 业务服务 → MQTT发布 → 网关转发 → 设备执行
```

### 4️⃣ 规则引擎流程
```
数据触发 → 条件判断 → 动作执行 → 告警通知/设备控制
```

## 技术栈

### 🏗️ 核心框架
- **微服务**: Spring Boot 3.2.0 + Spring Cloud
- **依赖注入**: Spring Framework
- **安全框架**: Spring Security + JWT
- **服务发现**: Nacos
- **配置中心**: Nacos Config

### 📨 消息通信
- **消息队列**: RocketMQ
- **协议支持**: MQTT (Eclipse Paho)
- **缓存**: Redis
- **服务调用**: OpenFeign

### 💾 数据存储
- **关系数据库**: MySQL (业务数据)
- **时序数据库**: TDengine (设备数据)
- **ORM框架**: MyBatis-Plus

### 🔧 开发工具
- **代码简化**: Lombok
- **对象映射**: MapStruct
- **JSON处理**: Jackson + FastJSON
- **构建工具**: Maven

## 数据流向说明

### 上行数据流 (设备→云端)
1. **设备连接**: IoT设备通过MQTT/HTTP/TCP连接到网关
2. **协议解析**: 网关根据协议类型进行消息解析
3. **身份认证**: 验证设备凭证，生成JWT Token
4. **消息路由**: 将设备数据发送到RocketMQ消息队列
5. **异步处理**: CommandReceiver消费消息进行业务处理
6. **数据存储**: 属性数据存储到TDengine，业务数据存储到MySQL
7. **规则匹配**: 触发规则引擎进行场景联动

### 下行数据流 (云端→设备)
1. **指令发起**: 管理后台或规则引擎发起设备控制指令
2. **API调用**: 通过Feign接口调用业务服务
3. **消息发布**: MqttPublisher发布控制消息
4. **网关转发**: 网关接收并转发指令到目标设备
5. **设备执行**: 设备接收指令并执行相应动作
6. **结果反馈**: 设备将执行结果上报回云端

## 关键特性

### 🚀 高性能
- 异步消息处理，支持大并发
- Redis缓存提升响应速度
- 时序数据库优化存储性能

### 🔒 安全可靠
- 多层次身份认证机制
- JWT Token安全传输
- 细粒度权限控制

### 🎯 灵活扩展
- 微服务架构支持水平扩展
- 插件化规则引擎
- 多协议支持便于设备接入

### 📊 实时监控
- 设备在线状态实时监控
- 消息统计分析
- 系统健康检查

## 部署架构建议

### 生产环境
- **网关层**: 负载均衡 + 多实例部署
- **业务层**: 按功能模块拆分部署
- **数据层**: 主从复制 + 读写分离
- **消息队列**: 集群部署保证高可用

### 开发环境
- 单机部署所有服务
- 使用内嵌数据库快速启动
- 本地配置简化开发流程

---

*本文档描述了云链IoT平台的整体架构和核心流程，为开发和运维提供参考。* 